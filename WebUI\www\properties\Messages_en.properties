btUpdate = Save
btnUpdate = Update
btRun = Run
btnExit = Exit Setup
btnNext = Next
btnBack = Back
btnExit1 = Exit Setup
btnNext1 = Next
btnBack1 = Back
btnExit3 = Exit Setup
btnNext3 = Next
btnBack3 = Back
btnBack4 = Back
helpName = Help
lKickOffUser = Forced to exit the system since other user login.
quickSetupName = Quick Setup
LogOutName =  Log Out
lableWelcome = Welcome
# lTitle = ASR Wireless Router UAPX-88
# lTitleUAPXB = ASR Wireless Router UAPXB-88
# lTitleUAPXC = ASR Wireless Router UAPXC-88
# lTitleAPX = ASR Wireless Router APX-88
# lTitleShareFile = ASR Wireless Router APX-88 , Share SD Card File
lTitle = Wireless Router UAPX-88
lTitleUAPXB = Wireless Router UAPXB-88
# Begin Add by ycc, modify locale
# lTitleUAPXC = Wireless Router UAPXC-88
lTitleUAPXC = Wireless Router XY695
# End Add by ycc, modify locale
lTitleAPX = Wireless Router APX-88
lTitleShareFile = Wireless Router APX-88 , Share SD Card File
lloginfailed = Invalid username or password
lnoconn = No connection
laUsername = Username
laPassword = Password
btnSignIn = Sign In
btnSharedFile = Shared File
# Begin Add by ycc, modify locale
# lacopyright = Copyright 2017, All Rights Reserved, ASR Ltd.
lacopyright =
# End Add by ycc, modify locale
lEnabled = Enabled
lDisabled = Disabled
lAllow = Allow
lDeny = Deny
lVisible = Visible
lInvisible = Invisible
lAlways = Auto
lOnDemand = OnDemand
lManual = Manual
lFull = Full
lRestricted = Restricted
lAscii = Ascii
lHex = Hex
lIAccess = Internet Access
lUSBAccess = USB Storage Access
ErrTimeZoneNotFound = Time Zone Not Found
ErrInvalidName = Device name should not contain special characters like # , : , ' '(space), & , ; , ~ , | , < , > , $.
btnOk = OK
btnOK = OK
btnOK_PDP = OK
btnCancel = Cancel
btnTriggerOk1 = Ok
btnTriggerCancel = Cancel
tableNoData = No entries found.
XMLExtError = Only XML extension file can be uploaded.
BinExtError = Only bin extension file can be uploaded.
lSignIn = Sign In
btnTriggerOk = OK
h1PleaseWait = Please wait...
# Begin Add by ycc, modify locale
# lAlert = ASR Wireless Router
lAlert = Wireless Router
# End Add by ycc, modify locale
btnModalSave = Save
h1RouterMessageHeader = ASR Wireless Router
lErrorPost = Ajax communication error with server,please retry.
lErrorTimeOut = No response. Please check your network connection
lIPSettingInvalid = IP setting is illegal, it is a value from 0 to 255
MAC_ADDR_IS_EMPTY = MAC address can't be empty.
MAC_ADDR_EXIST = MAC address has exist.
IP_ADDR_IS_EMPTY = IP address can't be empty.
lMacIpExist = MAC or IP address has exist.
lEmptyName = Name can't be empty.
lRuleNameIsChinese=Rule name does not support Chinese characters


#User management
lUsername = Router Username:
lPassword = Router Password:
lRePassword = Re-enter Password:
lPassErrorMes = Password do not match
lminLengthError = Username and Password must be min 4 characters
lmaxLengthError = Username and Password must be max 20 characters
lAccountExist = User name already exists

ErrInvalidUserPass = Username and Password should not contain special characters like ! , @ , # , : , ' '(space), & , ; , ~ , | , < , > , $.
btnAddNewAccount = Add New Account
lAccountTabletitle = User Accounts
ltAccountName = User Name
ltAccountGroup = Access Level
ltAccountDelColumn = Action
h1AccountEdit = User Account
lAccountName = Router Username:
lAccountPassword = Router Password:
lReAccountPassword = Re-enter Password:
lAccountAuthority = Access Level
opt_standard = Standard
opt_restricted = Restricted
lStandard = Standard
lRestricted = Restricted
btnOk = OK
lRemove = Delete
lMaxAccountError = Maximum 4 accounts are allowed
opt_restricted = Restricted
opt_standard = Standard

#Wireless Network Settings
lWNS = Wireless Network Settings
lWN = Status:
l80211_mode = 802.11 Mode:
lChannel = Primary Channel:
lBandwidth = Channel Bandwidth:
lMaxClients = Maximum Simultaneous Clients:
lShowAdvancedSetting = Show Advanced Settings
sSleepTimeUint = in Minutes(10~60)
dropdown80211n = 802.11n(b/g compatible)
dropdown80211bg = 802.11 b/g
dropdown80211b_only = 802.11 b only
dropdown80211g_only = 802.11 g only
dropdownWirelessAuto =Automatic
dropdownCH1 = CH 1 - 2412 MHz
dropdownCH2 = CH 2 - 2417 MHz
dropdownCH3 = CH 3 - 2422 MHz
dropdownCH4 = CH 4 - 2427 MHz
dropdownCH5 = CH 5 - 2432 MHz
dropdownCH6 = CH 6 - 2437 MHz
dropdownCH7 = CH 7 - 2442 MHz
dropdownCH8 = CH 8 - 2447 MHz
dropdownCH9 = CH 9 - 2452 MHz
dropdownCH10 = CH 10 - 2457 MHz
dropdownCH11 = CH 11 - 2462 MHz
dropdownCH12 = CH 12 - 2467 MHz
dropdownCH13 = CH 13 - 2472 MHz
dropdownBWAuto = Automatic (20/40 MHz)
dropdownBW20 = 20 MHz
dropdownBW40 = 40 MHz
# Begin Add by ycc, modify locale
# lDisableWifiAutoOff = I want to disable wifi atuo off function.
lDisableWifiAutoOff = I want to disable wifi auto off function.
# End Add by ycc, modify locale
lWifiAutoOffFun = Wifi Auto Off Function
lBand40AcsSwitchEnable = Band40 ACS Switch
WifiModeInvalideTip = WEP Security mode don't support 802.11n,please modify wireless security mode.
l80211_mode_50G = 802.11 Mode:
lrfband = RF Band:
lBeaconPeriodSetting = Beacon Period Setting
lBeaconPeriodErrorLogs = Beacon Period  can only be set the number from 50 to 4000
lSleepTimeErrorLogs = Sleep time can only be set the number from 10 to 60
lDTIMIntervalSetting = DTIM Interval Setting
lDTIMIntervalErrorLogs = DTIM Interval can only be set the number from 1 to 100
lMaxClientErrorLogs = MAX clients should be number from 1 to
sBeaconPeriod = (in msec(50~4000))
lChannelChoose= Secondary Channel
ApIsolateSwitchLabel = AP Isolate Switch
EnableApIsolateSwitch = Open
DisabledApIsolateSwitch = Close


#Time Setting
lYear = Year\Month\Day:
lMonth = Month:
lDay = Day:
lHour = Hour:Minute:Second(24H):
lMinute = Minute:
lSecond = Second:
lManualTimeSetting = System start setting
btUpdate_time = Save
pwaitUpdatetime =  Updating time,please waiting...
pSuccesscompleteupdateTime = Completed update time, please confirm and go on
pFailedcompleteupdateTime =  Failed to update time
pUpdateTimeOut = Update time from Network timeout, please retry
btSaveDateTime = Save Current Time
btUpdateNTPtime = Update to Network Time
lSyncupwithNtpTimeTip = I want to get NTP time when system start
lTimeErrorMes = Date or Time format is not correct
lEmptyNTPServerIP = EMPTY_NTPSERVERIP
lEmptyYear = EMPTY_YEAR
lEmptyMonth = EMPTY_MONTH
lEmptyDay = EMPTY_DAY
lEmptyHour = EMPTY_HOUR
lEmptyMinute = EMPTY_MINUTE
lEmptySecond = EMPTY_SECOND
lYearNumErr = only number is allowed in year box
lYearLenErr = year length should shorter than 5
lMonthNumErr = only number is allowed in month box
lMonthLenErr = month range should be in number 1 to number 12
lDayNumErr = only number is allowed in day box
lDayRangeLeap = day range should be in number 1 to number 29 in leap year month 2
lDayRangeNonLeap = day range should be in number 1 to number 28 in non-leap year month 2
lDayRangeErr = day range should be in number 1 to number 31 in this month
lDayRangeErr1 = day range should be in number 1 to number 30 in this month
lHourNumErr = only number is allowed in hour box
lHourRangeErr = hour range should be in number 0 to number 23
lMinuteNumErr = only number is allowed in minute box
lMinuteRangeErr = minute range should be in number 0 to number 59
lSecondNumErr = only number is allowed in second box
lSecondRangeErr = second range should be in number 0 to number 59
lTimeFormatError = only number is allowed
lNTPServerIPErr = IP address is not four number
lNTPServerIPNumErr = NTP server address is only number if you set it in IP foramt
lNTPServerIPRangeErr = NTP server address is only number and should be less than 255 if you set it in IP foramt
lNTPServer = NTP Server IP
lTimeZone = TimeZone
lselTZGMTEast12 = GMT+12 Auckland, Wellington, Fiji, Marshall islands
lselTZGMTEast11 = GMT+11 Magadan, New caledonia, Solomon islands
lselTZGMTEast10 = GMT+10 Canberra, Guam, Melbourne,Sydney,Vladivostok
lselTZGMTEast9 = GMT+9 Sapporo, Osaka, Seoul, Tokyo, Yakutsk
lselTZGMTEast8 = GMT+8 Beijing ,Chongqing ,Hongkong, Urumqi
lselTZGMTEast7 = GMT+7 Jakarta, Bangkok, Hanoi
lselTZGMTEast6 = GMT+6 Astana, Dhaka, Ekaterinburg
lselTZGMTEast5 = GMT+5 Tashkent, Islamabad, karachi
lselTZGMTEast4 = GMT+4 Moscow, Yerevan, Tbilisi St.Petersburg ,Muscat
lselTZGMTEast3 = GMT+3 Nairobi, Kuwait, Riyadh
lselTZGMTEast2 = GMT+2 Cairo, Helsinki, kaliningrad, South Africa ,Warsaw
lselTZGMTEast1 = GMT+1 Berlin, Brussels, Copenhagen, Madrid, Paris, Rome
lselTZGMT = GMT Greenwich
lselTZGMTWest1 = GMT-1 Dublin, London, Lisbon, Casablanca
lselTZGMTWest2 = GMT-2 Ascencion, Saint, Helena
lselTZGMTWest3 = GMT-3 Brasilia, Buenos, Aires, Georgetown, Falkland-islands
lselTZGMTWest4 = GMT-4 Atlantic time (Canada),  Caracas,  La-paz
lselTZGMTWest5 = GMT-5 Eastern time(USA and Canada), Bogota,  Lima,  Quito
lselTZGMTWest6 = GMT-6 Central time(USA and Canada), Mexico City
lselTZGMTWest7 = GMT-7 mountain time(USA and Canada),  Arizona
lselTZGMTWest8 = GMT-8 Pacific time(USA and Canada), Tijuana
lselTZGMTWest9 = GMT-9 Alaska
lselTZGMTWest10 = GMT-10 Hawaii
lselTZGMTWest11 = GMT-11 Midway Islands,  Samoa islands
lselTZGMTWest12 = GMT-12 Eniwetok, Kwajalein

lNtpStatus	= NTP Status
lNtpEnabled = Enabled
lNtpDisabled = Disabled

#DHCP Settings
lDhcpSettings = DHCP Settings
lDhcpServer = DHCP Server:
lDhcpConfSet = DHCP Configuration Settings
lDhcpRange = DHCP Range:
lDhcpStartAdd = DHCP Start Address:
lDhcpEndAdd = DHCP End Address:
lDevLanIP = Router LAN IP:
ldhcplt = DHCP Lease Time:
lDnsName1 = DNS1 IP Address:
lDnsName2 = DNS2 IP Address:
lDnsIpError = DNS IP Address invalid
lMinutes = (in Seconds)
lIPErrorMsg = Enter valid IP Address
lErrorNumber = Enter numeric values for DHCP Lease Time
lStaticIP_MAC = MAC Address:
lStaticIP_IP = IP Address:
btnAddStaticIP  =  Add Static IP
h1AddStaticIP =  Static IP setting
ltMacAddress = MAC Address
ltIPAddress = IP Address
lMinutes = (in Seconds)
btnAdd_dhcp = Add
lDhcpAddrRangeError = DHCP start address is larger than end address.
lDhcpStartAddrError = DHCP start address must be from 192.168.X.2 at least.
MaxStaticIpError	= Maximum 30 static IP are allowed
StaticIpAndRangeNotSameError	= Please make sure the DHCP range and the static ip in same network segment.
DHCPV6title = DHCPV6 Settings
lDhcpV6Server = DHCPV6 Server:
lstatelessServer = Stateless Server
lstatefullServer = Stateful Server
lt_dhcp_stcRedirectionFunction = Redirection Function
lt_dhcp_stcRedirectionURLEnabled = Enabled
lt_dhcp_stcRedirectionURLDisabled = Disabled
lt_dhcp_stRedirectionURL = Redirection URL
lStaticIpAddrError = Invalid IP address,address range should be 
DNSEnableSwitchLabel = DNS Configure Switch
ClosedivDNSEnableSwitch = Disabled
OpendivDNSEnableSwitch = Enable

#Inter Connection settings
lICS = Internet Connection Setup
lMICI = My Internet Connection is
lMIsp = My ISP:
disptext2 = I want to update the ISP database
lManualNetworkStart = Manually select network when system start
lCustomDNS = Custom DNS
dispmanualnetworktext = I want to manually select network when system start
disptext3 = I want to override the default DNS settings
disptext4 = I want to clone MAC Address
btGetMAC = Get My Computer\u2019s MAC Address
lIP_Address = IP Address:
lMask = Mask:
lGateway_Address = Gateway Address:
lPrimDNSAdd = Primary DNS Address:
lSecondDNSAdd = Secondary DNS Address:
lCustomeDNS1 = Custom DNS1:
lCustomeDNS2 = Custom DNS2:
lmaxLengthError64 = Password must be max 64 characters
lminLengthError8 = Password must be min 8 characters
lChineseCharError = Password can not hava Chinese characters
lLengthError5 = Password should 5 characters only
lLengthError13 = Password should 13 characters only
lLengthError8_64 = Password must be min 8 and max 64 ascii characters
lLengthError8_64hex = Password must be min 8 and max 64 hex characters
lISPUserName =  Username:
lISPUserName1 = Username:
lISPPassword1 = Password:
lProfile = Selected Profile:
lMannualNetwork = Selectable Network:
lNetwork = Selected Network:
lPUsername = Username:
lPPassword = Password:
lAccessNumber = Access Number:
ltadvanceds = Advanced Settings
h1AddNewProfile= Add New Profile
h1MannualNetwork= Manual Network
h1BGScanTimePopup= Background Scan
btnUpdateISP = Add New Profile
h1EditProfile = Edit Profile
lAuthType = Authentication Type:
lConnMode = Connection Mode:
lBgScanTime = LTE background scan network time
CurrentScanModeLabel = Current Select Network Mode
AutoSelectNWMode = Automatic Mode
ManualSelectNWMode = Manual Mode
UnkownSelectNWMode = Unknown Mode
lIdle = Idle Time:
lProfilename = Profile Name:
lAPN = 2G/3G APN Name:
lLteAPN = LTE APN Name:
lCUsername = Username:
lCPassword = Password:
lCAccessNumber = Access Number:
lCAuthType = Authentication Type:
lCConnMode = Connection Mode:
lCIdle = Idle Time:
lISPPass = Password:
lseconds = (seconds)
ltProfilename = Profile Name
ltActive = Active
ltAPNname = APN Name
ltConnMode = Connection Mode
lEmptyUserName = Empty username is not allowed.
lEmptyPassword = Empty password is not allowed.
EMPTY_PROFILE_NAME = Empty Profile name is not allowed
EMPTY_APN_NAME = Empty APN name is not allowed
EMPTY_USER_NAME = Empty  username is not allowed
EMPTY_PASSWORD = Empty password is not allowed
EMPTY_ACCESS_NUMBER = Empty access number is not allowed
NOT_VALID_IDLE = Idle time should be greater than zero.
lWUsername = Username:
lWPassword = Password:
h1Configure = Preset Username And Password For Webportal
lWorkMode= Network Mode
lVersionSwitch = Version Switch
lBootMode= Preferred Network Mode
lBootMode1= Preferred Network Mode
lBootMode2= Preferred Network Mode
lBootMode3= Preferred Network Mode
lBootMode4= Preferred Network Mode
dropdown_auto = Auto
dropdown_manual = Manual
lsetLikeLTEType= Preferred LTE Type
dropdownDisable = Disabled
dropdownEnable = Cellular
dropdownMultimode = 4G/3G/2G multimode
dropdown4Gonly = 4G only
dropdown5Gonly = 5G only
dropdown45Gonly = 4G/5G
dropdown43Gonly = 4G/3G
dropdown32Gonly = 3G/2G
dropdown3Gonly = 3G only
dropdown2Gonly = 2G only
dropdownDisableNw = Auto
dropdown5GPre = 5G Preferred
dropdown4GPre = 4G Preferred
dropdown3GPre = 3G Preferred
dropdown2GPre = 2G Preferred
dropdown2GPreInBootMode2 = 2G Preferred
dropdown3GPreInBootMode2 = 3G Preferred
dropdownTDPre = TD-LTE preferred
dropdownFDDPre = LTE FDD preferred
dropdown14GPre = 4G Preferred
dropdown13GPre = 3G Preferred

lPDP_default = PDN1 Settings:
lPDP_dedicate = PDN2 Settings:
pPDPdef_lable = PDN1(Default Bearer)
sPDPdef_lable = Dedicated Bearer1
pPDPded_lable = PDN2(Default Bearer)
s1PDPded_lable = Dedicated Bearer1
s2PDPded_lable = Dedicated Bearer2
s3PDPded_lable = Dedicated Bearer3
pPDPdef_chk = Setting
sPDPdef_chk = Setting
pPDPded_chk = Setting
s1PDPded_chk = Setting
s2PDPded_chk = Setting
s3PDPded_chk = Setting
sPDPtft_lable = TFT Setting
s1PDPtft_lable = TFT Setting
s2PDPtft_lable = TFT Setting
s3PDPtft_lable = TFT Setting
btnAddTFTRule  = Add

h1Popup_PDP = PDP Setting
lRuleename = PDP connection name
lIPType = IP type
dropdown_IPV4 =  IPV4
dropdown_IPV6 =  IPV6
dropdown_IPV4V6 = IPV4V6
lQOSQCI = QCI level
lQOSEnbaletitle = Enable QOS
lQciCheckError = QCI must be an integer and greater than zero.

l2G3GAuthType = 2G3G Authentication Type
l2G3GUser	= 2G3G User Name
l2G3GPassword = 2G3G Password
l4GAuthType = LTE Authentication Type
l4GUser	= LTE User Name
l4GPassword = LTE Password
APN_AHTU_USER_NAME_INVALIDEATE=User name does not support chinese characters

lTFTIPAddress = Remote IP Address
IPV4Radio = IPv4
IPV6Radio = IPv6
lTFTTabletitle = TFT Table
lLocalPort = Local Port Range
lRemotePort = Remote Port Range
ltTFTname = TFT rule name
ltIPType = IP Type
ltRemoteIP = Remote IP
ltLocalPort = Local Port
ltRemotePort = Remote Port
lRuleName_TFT = TFT Rule Name
btnAddNewTFTRule = Add
h1TFTTableRule = TFT Rule Table
h1AddTFTRule = Add TFT Rule
lpacketfilter_index = Packet Filter Identifier
levaluation_index = Evaluation Precedence Index
ldirection = Direction
dropdown_uplink = Uplink
dropdown_downlink = Downlink
lIPAddress_TFT = IP Address
lSubnetmask_TFT = Subnet Mask
lprotocol_TFT = Protocol Number
lIPV4Address_TFT = IP Address
lV4Subnetmask_TFT = Subnet Mask
lemptyTFTname = Empty TFT rule name
lspecialTFTname = Should not have special charactors in TFT rule name
lLocalPortInvalid = Invalid local port,the port value range is from 1 to 65535.
lRemotePortInvalid = Invalid remote port,the port value range is from 1 to 65535.
lRuleNameIsChinese = Rule name don't support chinese character
lIncorrectIPAddress = Invalid Remote IP Address
lIncorrectNetMaskAddress = Invalid NetMask Address
lDataPacketFilterIdError = Invalid Packet Filter Identifier.The value range is from 1 to 8 and must be an integer.
lAssessPrioritiesError = Invalid Evaluation Precedence Index.The value range is from 0 to 255 and must be an integer.
lProtocolNumError = Invalid Protocol Number.The value range is from 0 to 255 and must be an integer.
lsubsetMaskError = Subset mask format error.
lRoamingDisableAutoDialTip = prohibit automatic dial-up in the roaming status.
lautoSwitch = Auto switch version
lMTULabel = MTU
lMtuInvalidTip = Invalid MTU value.The value range is from 1000 to 1500 and must be an integer.
APN_NAME_INVALIDEATE = APN name invalidate,don't support chinese and special characters percent(%),comma(,),semicolon(;),caret(^).
lAutoAPNLabel = Auto APN
lAutoConfigureAPNCheckBox = Auto Configure APN


DialInRoamingLabel = Dial in Roaming
EnabledDialInRoaming = Enable
DisabledDialInRoaming = Disabled

EngineeringModelLabel = Engineering Mode
OpenEngineeringModel = Enabled
CloseEngineeringModel = Disabled
queryTimeIntervalLabel = Query Time Interval
queryTimeIntervalUnitLabel = Minute

#Manual Network
h1ManualScanNetwork = Manual Scan Network
btUpdate1 = Manual Scan Network
btnConfirm = Confirm
dropdownImmediate = Immediate
dropdown30sec = 30 seconds
dropdown1M = 1 minutes
dropdown3M = 3 minutes
dropdown5M = 5 minutes
dropdown10M = 10 minutes
dropdown15M = 15 minutes
dropdown30M = 30 minutes
dropdown60M = 60 minutes
dropdownLteTimeDisable = Disable
dropdownAuto = Auto
lManualPromte = &nbsp; &nbsp; It will take more than one minute to manual scan network.Please waiting...
completeScanNetwork	= Network scan completed.You can choose network now!
waitScanNetwork =  Searching network,please waiting...
# Begin Add by ycc, modify locale
#selectEmptyNetworkTypeErrorTip = Seleced network can't be empty.
selectEmptyNetworkTypeErrorTip = Selected network can't be empty.
# End Add by ycc, modify locale
lScanNetworkError = Search network failed,please scan network again!
lScanNetworkTimeOut = Search network operation time out,please scan network again!
lSimCardAbsent		= SIM is absent,please insert SIM card to search network!
lPinEnable		= SIM card is locked, please enter the PIN code to unlock the SIM card.
lPukEnable		= PIN code is locked, please enter PUK code to unlock PIN.

#Message
lMessageStatsFrom = From
lMessageStatsSubject = Subject
lMessageStatsReceived = Received
lMessageStatsStatus = Status
lMessageStatsLocation = Location
lUnread = Unread
lReaded = Read
lUnsent = Unsent
btnSendOK = OK
lSent = Sent
btnSend = Send
onlySendOneSms = Only send one message.
SendSMSbtn = SendSMS
contactID = TO:
contentID = Content:
sendFail = Send Message Failed!
sendSucess = Send Message Successfully!
h1SendResult = Send Message Result
lMessageReportTitle = Send Message Report
lMessageReportSuccessReceive = received sms successfully.
lMessageReportFailedReceive = received sms failed.


h1delsmsConfigure = Delete Confirm
h1MessageContent = Display Message Content
lDeleteMessage =  &nbsp; &nbsp;Do you want to delete the SMS selected?
lMessageFull = &nbsp; &nbsp; Message box is FULL, please delete unused messages!
btUpdate_Delete = Delete
btnOK_confirm = Confirm

#WiFi Hotspot
btnScanWirelessNw = Scan wireless networks
ltWirelessNws = Choose a wireless network to connect
lpsk = Network key:
lRetypepsk = Confirm network key:

#primary network
WifiFrequencyBand = Wifi Frequency Band:
lNetwork = Network:
lNwStatus = Network is:
lSSID = Network Name (SSID):
lNwVisiStatus = Network Visibility Status:
lWireSecurity = Wireless Security:
lpass = Password:
lunmaskpass = Unmask Password
lwpa = WPA Cipher:
lAuth = Authentication:
lEncryption = Encryption:
lKeyType = Key Type:
lRetypePassword = Re-enter Password:
lInvalidPassword = Invalid Password
lPasswdSpaceError = Password cannot contain spaces
lAsciiPasswdLength5Error = Password should be ASCII character and length must be 5
lAsciiPasswdLength13Error = Password should be ASCII character and length must be 13
lHexPasswdLength10Error = Password should be hexadecimal character(0-9,a-f,A-F) and length must be 10
lHexPasswdLength26Error = Password should be hexadecimal character(0-9,a-f,A-F) and length must be 26
lWiFiProtSetup = WiFi Protected Setup:
btnAddWPSClient = Add WPS Client
btnResetToWPSDefault = Reset to WPS Defaults
lexternalSorce =  This router can be configured from an external source.
lRouterPin = Router PIN:
h1AddWPSClient = Add WPS Client
lCancelWpsSession = Cancel Session
lWPStext = Choose a method to associate WPS certified Client
lWpsMatchPro = WPS is matching...
lWpsMatchSuccess = WPS match successfully
lWpsMatchFailed = WPS match failed
lWpsMatchInterrupt = WPS match interrupt
lWpsPinCheckFail	= PIN check failed
spanWPSPushButton = WPS Push Button
lWpsCfgBtn = Add WPS Client
lNoneEncrypModeTip = Wireless network is not encrypted mode, whether or not to continue?
lWpsDisabledInWepMode = WPS is not available in WEP encryption mode, whether or not to continue?
lWpsDisabledInSsidHideStatus = WPS is not available in network invisible status, whether or not to continue?
spanEnterPin = Enter PIN
btnPush = Push
btnRegister = Register
btnClose = Close
lnoWPSforWAPI = WPS is not supported for WAPI-PSK security mode.
lnoWEPfor11n = WEP is not available in 802.11n mode.
lssidlenError = The max length of ssid is 32.

WPSStatus0 = Session not in progress.
WPSStatus1 = Session attempt successful.
WPSStatus2 = PIN Session active.
WPSStatus3 = Session not started.
WPSStatus4 = Push Button Session active.
WPSStatus5 = PIN Checksum failed.
lWPSPinError = The pin must be exactly 8 or 4 numerical characters.

dropdownNone = Disabled
dropdownWPA2 = WPA2-PSK
dropdownWPAWPA2 = WPA-WPA2 Mixed
dropdownWPA = WPA-PSK
dropdownWEP =  WEP
dropdownWAPI = WAPI-PSK
dropdown64b = 64 bit - 5 ASCII/10 Hex Characters
dropdown128b = 128 bit - 13 ASCII/26 Hex Characters
dropdown64bASCII = 64 bit - 5 ASCII Characters
dropdown64bHEX = 64 bit - 10 HEX Characters
dropdown128bASCII = 128 bit - 13 ASCII Characters
dropdown128bHEX = 128 bit - 26 Hex Characters
AES_Stronger = AES (Stronger)
TKIP_Strong = TKIP (Strong)

#Dashboard

h2Internet = Internet
h2Router = Router
h2HomeNw = Home Network

SPdpInformation = PDP Information
Sconnum = PDP Connection Numbers
SpdpType = PDP Type
SpdpSuccess = PDP Connection Status
SIPV4Address = IPv4 Address
SIPV4DNS = IPv4 DNS Server
SIPV4Gateway = IPv4 Default Gateway
SIPV4Netmask = IPv4 Network Mask
SIPV6Address = IPv6 Address
SGIPV6Address = Global IPv6 Address
SIPV6DNS = IPv6 DNS Server
SIPV6Gateway = IPv6 Default Gateway
SIPV6Netmask = IPv6 Network Mask
pDashCurConnTime = Current Connect Time
pDashTotalConnTime = Total Connect Time
SPDPType_Default = Default
SPDPType_Custom = Custom Made
h3CellularConnection = Cellular Connection
h3WiFiConnection = WiFi Connection
h3IntrenetConnection = Internet Connection
h3IntConn = Traffic Statistics
h3RouterLANIP = Router LAN IP
h3RouterInfo = Router Information
h3Firewall = Firewall
h3UsbFileSharing = USB File Sharing
h3DynamicDNS = Dynamic DNS
h3SoftInfo = Software Information
h3ConnDevice = Connected Devices
h3Wireless = Wireless
h3WirelessS = Wireless Settings
h3WPS = WiFi Protected Setup
h3DhcpServer = DHCP Server

pCellular = Cellular Data Connectivity:
pCellularMode = Cellular Network Mode:
pSignalStrength = Signal Strength:
pRoaming = Roaming:
pSIMStatus = SIM Status:
pPINStatus = PIN/PUK Status:
pWanStatus = WAN Link Status:
pWanConnStatus = Connection Status:
pConnType =  Connection Type:
pDashIPAddress =  IP Address:
pDashConnTime = Total Connection Time:
pDashDefaultGateway = Default Gateway:
pDashNetworkMask = Network Mask:
pdashDNS = DNS Server:
pSentPackets = Sent Packets:
pRecPackets = Received Packets:
pDeviceModel = Device Mode:
pSoftVersion = Software Version:
pHardVersion = Hardware Version:
pDashRouterLANIP = Router LAN IP:
pDashRouterImeiTag=IMEI:
pDashRouterMAC = Router MAC:
pDashRouterRunTime = Run Time:
pDashBetteryInfo = Battery Charging:
pDashBetteryVol = Battery Electric Quantity:
pDashRouterMask = Router Network Mask:
lUncharged = Uncharged
lCharging = Charging
lFullycharged = Fully charged
lNoBattery = No Battery
lconnected = connected
ldisconnected = disconnected
lconnecting  = connecting
ldDays = Days
ldDay = Day
ldHours = Hours
ldHour = Hour
ldMinutes = Minutes
ldMinute = Minute
ldSeconds = Seconds
ldSecond = Second
pISPName = Network Operator:
pRoamingNetworkOperator = Roaming Network Operator:

h2AutoApn = Auto APN List
pDashAutoApn_mmcTag = MCC:
pDashAutoApn_mncTag = MNC:
pDashAutoApn_OperatorNameTag = Operator Name:
pDashAutoApn_ApnTag = APN:
pDashAutoApn_LteApnTag = LTE APN:
pDashAutoApn_NetworkTypeTag = Network Type:
pDashAutoApn_authtype2g3gTag = 2G3G Auth Type:
pDashAutoApn_username2g3gTag = 2G3G User Name:
pDashAutoApn_password2g3gTag = 2G3G Password:
pDashAutoApn_authtype4gTag = 4G Auth Type:
pDashAutoApn_username4gTag = 4G User Name:
pDashAutoApn_password4gTag = 4G Password:
pDashAutoApn_iptypeTag = Ip Type:



# pUpdatesAvailable = Updates Available
pWirelessNw = Wireless Network:
pWiFiWirelessNw = Wireless Network:
pSecurityMode = Security Mode:
pChannelNumber = Channel Number:
pWirelessPNw = Primary Wireless Network:
pWirelessGNw = Other Wireless Network:
lrestartWB = Data connection is up, please restart the web browser.
lPValue = (Security:
lGValue = (Security:
lSIMPresent = Present
lSIMAbsent = Absent
lSIMerror = Unknown error
resetTraffic = Reset


#Connected Device
ltName = Name
ltIpAddress = IP Address
ltDeviceStatus=Status
ltMac = MAC Address
ltBlocked = Blocked
btnModalOk = OK
btnModalReset = Remove Device Entry
lModalHeader = Device Name:
h1DeviceHeader =  Edit Device
ltStatus = Internet Access
ltStatus1 = Status
ltTime =  Connected time
State = Disable
#Disable device
Disable_Devices = Disable Device
DisableMac = MAC Address
DisableName = Disable Name
Recover = Recover
#Firewall Settings
lFirewallSettings = Firewall Settings
lVPN = Virtual Private Network (VPN)
lFirewallProtection = Firewall Protection:
lIPSecPassthrough = IPSec Passthrough:
lPPTPPassthrough = PPTP Passthrough:
lL2TPPassthrough = L2TP Passthrough:
lBlock = Block
lUnblock = Unblock

#Network Activity
lLogintime = Last Login Time:
ltDialLogDeleteBtn = Delete
ltClientLogDeleteBtn = Delete

#DDND Settings
lDDNS = Dynamic Domain Name System (DDNS)
lDDNSService = DDNS Service:
lDDNSUsername = Username:
lDDNSPassword = Password:
lDDNSHostname = Hostname:
lDDNSType = Type:
lDDNSWildcard = Wildcard:
lDDNSErrUsernameEmpty = Username can not be empty.
lDDNSErrPasswordEmpty = Password can not be empty.
lDDNSErrHostnameEmpty = Hostname can not be empty.

#Storage Settings
lStorageSettings = USB Device File Sharing:
lDeviceUsage = Storage Device Usage
ltSDName = Share Name
ltSDUsedSpace = Used Space
ltSDAvailableSpace = Available Space
btnSafelyRemoveDevices = Safely Remove Devices
lstoarageError = One or more of the USB devices can not be removed right now! \n Please try removing the device(s) after some time.
#Diagnostics
lSelTool = Select Tool
lEnterParams =  Enter Parameters
lOutput = Output
lSelectToolErrorMsg = Please select a tool first and then click on Run.
lInvalidCharErrorMsg = Illegal characters in the input! Re-enter the values.

#Layout Manager
tInternet = Internet
tHome_Network = Home Network
tWireless = Wireless
tDashboard = Dashboard
tRouter = Router
tStorage = Storage

mTrafficStats = Traffic Statistics
mNWActivity = Network Activity
mInternetConn = Internet Connection
mManulNetwork = Manual Network
mWiFiConn = WiFi Hotspot
mDDNS = DDNS
mTelnet = Telnet
mFile_Sharing = File Sharing
mReboot = Reboot Router
mTheme = Change Theme
mMessage = Message
mTimeSetting = Time Setting
mWebDav = SD Card File
# Begin Add by ycc, modify locale
#mWebDavmanagement = SD Card Managment
mWebDavmanagement = SD Card Management
# End Add by ycc, modify locale
mAcsManagement = ACS Management

# Begin Add by ycc, modify locale
#mPinPuk = PIN Managment
mPinPuk = PIN Management
# End Add by ycc, modify locale
lAttempts = PIN/PUK Attempts
lPINAttempts = PIN Attempts
lPUKAttempts = PUK Attempts
lProvidePin = Provide PIN
lResetPin = Reset PIN
lEnablePin = Enable PIN
lDisablePin = Disable PIN
lChangePin = Change PIN
lEnterPuk = Enter PUK:
lEnterPin = Enter Current PIN:
lEnterPin1 = Enter Current PIN:
lEnterNewPin = Enter New PIN:
lEnterNewPin1 = Enter New PIN:
lUnknownNoSIM = No SIM present or unknown error
lPukExhausted = This SIM card is permanently locked
lPinExhausted = All the PIN attempts to unlock the SIM are exhausted. Please provide PUK to reset PIN.
linvalidPin = Invalid PIN. Length of PIN must be 4 to 8 alphabetical characters.
lNewPinSameWithOld = New PIN and old PIN can't be same
linvalidPuk = Invalid PUK.  Length of PUK must be 4 to 10 alphanumeric characters.
lpinattempts = The number of PIN attempts left:
lpukattempts = The number of PUK attempts left:
btUpdate0 = Save
btUpdate2 =  Save
lPINrequired = required
lMEPUnlockrequired = Please unlock MEP in Mep setting page ,then manage PIN
lPinPasswordError =  PIN password incorrect
lSimNotSupportPinCode = SIM card do not support this PIN code
lSimPUKRequest = SIM PUK request
lFailedWithUnkown = Unknown cause errors


mMEPSetting = MEP Setting
lUnlockPINBtn = Unlock PIN
lEnableLockPINBtn = Enable PIN Lock
lMEPPNpasswordSetting =	PN Setting
lMEPPNpassword = PN Password
lUnlockMEPPNBtn = Unlock PN
lEnableLockMEPPNBtn = Enable PN Lock
lInvalidLockMEPPNBtn = Unavailable PN Lock
lMEPPNpukBtn = Unlock PN PUK
lMEPPUpasswordSetting =	PU Setting
lMEPPUpassword = PU Password
lUnlockMEPPUBtn = Unlock PU
lEnableLockMEPPUBtn  = Enable PU Lock
lInvalidLockMEPPUBtn = Unavailable PU Lock
lMEPPUpukSetting = PU PUK setting
lMEPPUpuk = PU PUK
lMEPPUpukBtn = PU PUK Unlock
lMEPPNpukSetting = PN PUK setting
lMEPPNpuk = PN PUK
lMEPSPpasswordSetting = SP Setting
lMEPSPpassword = SP password
lUnlockMEPSPBtn = Unlock SP
lEnablelockMEPSPBtn = Enable Lock SP
lInvalidLockMEPSPBtn = Unavailable SP Lock
lMEPSPpukSetting = SP PUK Setting
lMEPSPpuk = SP PUK
lMEPSPpukBtn = SP PUK Unlock
lMEPPCpasswordSetting = PC Setting
lMEPPCPpassword = PC Password
lUnlockMEPPCBtn = Unlock PC
lEnableLockMEPPCBtn = Enable PC Lock 
lInvalidLockMEPPCBtn = Unavailable PC Lock
lMEPSIMpasswordSetting = SIM Setting
lMEPSIMpassword = SIM Password
lUnlockMEPSIMBtn = Unlock SIM
lEnablelockMEPSIMBtn = Enable SIM Lock
lInvalidLockMEPSIMBtn = Unavailable SIM Lock
lMEPPNLeftRetry = MEP PN left retry number:
lMEPPUAttempts = MEP PU left retry number:
lMEPSPAttempts = MEP SP left retry number:
lMEPPCAttempts = MEP PC left retry number:
lMEPSIMAttempts = MEP SIM left retry number:
lMEPinvalidPin = Invalid MEP passord. password must be 0 to 16 alphabetical characters.

lMepSimPukSetting = SIM PUK Setting
lMepSimPuk = SIM PUK 
lMepSimPukAlertError = SIM PUK error 
lMepSimPukBtn = SIM PUK
lMepPcPukSetting = PC PUK Setting
lMepPcPuk = PC PUK
lMepPcPukAlertError = PC PUK error
lMepPcPukBtn = PC PUK Unlock
lPasswordError = Password Error


mConnected_Devices = Connected Devices
mAccess_Logs = Network Activity
mDHCP_Settings = DHCP Settings
mFirewall_Settings = Firewall Settings
mAccess_Control	= Access Control
mApp_Gaming = Application and Gaming
mPort_Forward = Port Forwarding

mWire_Set = Wireless Settings
mWire_Sec = Wireless Security Settings
mPNSS = Primary Network Security Settings
mONSS = Other Networks Security Settings
mWMAC = Wireless MAC Filters

mStorageSettings = Storage Settings

mUserManage = User Management
mConfManage = Configuration Management
mSoftUpdate = Software Upgrade
mDigo = Diagnostics
mTimeZone = Time Zone

mDashboard = Dashboard

#Software Upgrade
lSoftwareInfo = Software Information
lCurrentSoftVersion = Current Software Version:
lCurrentSoftwareDate = Current Software Date:
lSoftwareInfo = Software Information
lSoftwareInfoText = If you have a software upgrade file on your machine, you can use this option to manually upgrade the router.
lSoftwareWarningText = Warning!!! Please make sure you connect to MIFI using USB cable before upgrade.
lCheckNewSoft = Check for New Software
lCheckNewSoftText = You can check manually if newer version of software is available.
lAutomatic_Upgrade = Automatic Upgrade
btUpgrade = Upgrade
btCheck = Check
btSetAsADefault = Set As Default
h1RouterUpgrade = Router Upgrade
lUploading = Upgrade is in progress
lUpgrade = Upgrade is in progress
lDownload = Firmware downloading
lBurnFirm= Firmware burning

lReboot = Reboot is in progress. Please wait...
lSuccessReboot = Upgrade firmware successfully, rebooting...
lFailReboot = Upgrade firmware failed, rebooting...

lUpgradeError =  Upgrade status:
lWarning = W A R N I N G !
lReminder = Reminder
lWarningLine1 = Upgrading firmware may take a few minutes.
lWarningLine2 = Do not turn off the power or press the reset button!
ErrorEnterName = Enter Rule Name
lsoftwareError = Please select a file to upgrade
btGetSoftVersion = Browse
lUpgradeInfoText = Get software upgrade information from SD card.
h1SDUpgradePopup =  SD Upgrade
lSDUpgradeSuccessText = Upgrade firmware from SD card successfully
lSDUpgradeFailedText = Upgrade firmware from SD card Failed


#Time Zone
ldeviceTimeZone = Device Time Zone:


#ERROR
NET_ERR_STATUS_OK               =       OK
NET_ERR_SWUP_FILE_SIZE          =       File too big see: MAX_UPGRADE_SIZE
NET_ERR_SWUP_BAD_URL            =       Not an http or ftp url
NET_ERR_SWUP_CONNECTION         =       Server stalled or not responding
NET_ERR_SWUP_DOWNGRADE          =       Downgrade
NET_ERR_SWUP_REMOTE_NEEDED      =       Remote upgrade need
NET_ERR_SWUP_BAD_FILE           =       Image file corrupt
NET_ERR_SWUP_BURN_FAILED        =       Burn operation failed
NET_GENERIC_ERR                 =       Unknown Error
NET_ERR_REMOTE_NON_SUPPORT      =       Remote Not Supported
UNKNOWN_ERROR                   =       Unknown Error

#Application & Gaming
ltRuleName = Rule Name
ltAppName = App Name
ltExternalPorts = External Ports
ltInternalPorts = Internal Ports
ltLocalIPAddress = Local IP Address
ltProtocol = Protocol
ltTRuleName = Rule Name
ltTAppName = App Name
ltTExternalPorts = External Ports
ltTInternalPorts = Internal Ports
ltTLocalIPAddress = Local IP Address
ltTProtocol = Protocol
lDescription = You can provide custom rule with your settings
lPortForwordingText = Open multiple ports or a range of ports in your device and redirect data through those ports to a single device on your network. Static IP address assignment is recommended for local devices used for Port Forwarding.
lPortTriggeringText = Open single or multiple ports on your device when the device senses data sent to the Internet on a trigger port or port range. Special applications rules apply to all computers on your internal network.
h1PortFwdRule = Port Forwarding Rule
lAppName = Application Name:
lRulename = Rule Name:
lExtPortRange = External Port Range:
lLocalPortRange = Local Port Range:
lLocalIPAddress = Local IP Address:
lProtocol = Protocol:
h1PortTrgRule = Port Triggering Rule
lAppNameTrigger = Application Name:
lRulenameTrigger = Rule Name:
lExtPortRangeTrigger = External Port Range:
lLocalPortRangeTrigger = Local Port Range:
lProtocolTrigger = Protocol:
lAppAndGamingErrorFwd = IP address is not valid
lAppAndGamingErrorTrg = IP address is not valid
lStandardPortError = Standard Port Triggering rules can not be edited.
lAppAndGamingErrorFwdNum = Port should be number only.
lPortForwording = Port Forwarding
h1PortFwdRule = Port Forwarding Rule
h1PortTrgRule = Port Triggering Rule
MaxRuleError = Maximum 8 rules are allowed

mCustom_FW = Custom Firewall Rules
h1CustomFWRule = Custom Firewall Rule
lCustomFWRulesText = Custom Firewall Rules can be used to block the network traffic the using source IP address, source ports, destination IP address, destination ports or any combination of these parameters.
ltRuleName = Rule Name
ltEnabled = Enabled
ltSrcPort = Source Port
ltDstPort = Destination Port
ltSrcIP = Source IP Address
ltDstIP = Destination IP Address
lRuleName_fw = Rule Name:
lSrcPort = Source Port:
lDstPort = Destination Port:
lSrcIP = Source IP Address:
lSrcIP6 = Source IPV6 Address:
lDstIP = Destination IP Address:
lDstIP6 = Destination IPV6 Address:
lStatus_fw = Status:
lIPType = IP Type:
btnAddRule = Add Rule
lCustomFWSpecialCharsNotAllowed= Special characters like # , : , ' '(space), & , ; , ~ , | , < , > , $ are not allowed in the name.
lAtleastOne = Please provide atleast one of the source IP address, source ports, destination IP address and destination ports.
lIncorrectSrcIP = Incorrect source IP address
lIncorrectDstIP = Incorrect destination IP address
lIncorrectPort = Invalid port
lToFrmPort = Invalid port range
homeIPError = Please specify Local IP Address within Home Network range
lUplugAndPlay = Universal Plug n Play:
lPortFwdTrigSpecialCharsNotAllowed = Special characters like # , : , ' '(space), & , ; , ~ , | , < , > , $ are not allowed.
lPortTriggering = Port Triggering
lPortError = End port should be larger than start port
lIPFilterMode = IP Filter Mode
lDisabledFirewall = Port filter has been opened, firewall is not available.
IP6SpacesNotAllowed = Spaces are not allowed in IPV6 addresses

#Domain Name Filter
mDomain_Name_Filter = Domain Name Filter
lDNFilterRulesText = Custom Domain Name Filter Rules can be used to block the network traffic the using domain name, start IP address, end IP address or any combination of these parameters.
lt_DNFilter_btnAddRule = Add Rule
lt_DNFilter_RuleName = Rule Name
lt_DNFilter_Enabled = Enabled
lt_DNFilter_DomainName = Domain Name
lt_DNFilter_SrcIpStart = Start IP Address
lt_DNFilter_SrcIpEnd = End IP Address
h1DNFilterRule = Domain Name Filter Rule
lRuleName_DNF = Rule Name
lDomainName = Domain Name
lSrcIPStatrt = Start IP Address
lSrcIPEnd = End IP Address
lIncorrectStartIP = Incorrect start IP address
lIncorrectEndIP = Incorrect end IP address
lDNF = Domain Name Filter:
DNSDenyList = Deny DNS List
DNSAllowList = Allow DNS List
lDNSDenyListEmpty = Do not filter any domain names, automatically turn off the domain name filtering function
MaxDNSRuleError = Maximum 10 domain name filter rules are allowed

#Port Forwarding
lCustomPWRulesText = Custom Port Forwarding Rules
h1CustomPWRule = Port Forwording Rule
lRuleName_pw = Rule Name
lIP_pw = IP Address
lPort_pw = Port
lProtocol_pw = Protocol
ltPWRuleName = Rule Name
ltPWIP = IP Address
ltPWPort = Port Range
ltPWProtocol = Protocol
lIncorrectPWIP = Incorrect IP Address
lPWAtleastOne = Please provide atleast one of IP address, port range.
#Traffic Statistics
lTrafficStats = Traffic Statistics
lTrafficStatsInfo = Traffic Statistics displays the number of packets sent by your router.
lTrafficStatsWAN = Internet Connection (WAN)
lTrafficStatsLAN = Home Network (LAN)
lTrafficStatsUSB = USB Connection

lTrafficStatsWANSent = Sent
lTrafficStatsWANReceived = Received
lTrafficStatsWANErrors = Errors
lTrafficStatsWLAN = Wireless (WLAN)
lTrafficStatsWLANSent = Sent
lTrafficStatsWLANReceived = Received
lTrafficStatsWLANErrors = Errors

#Theme Change
lChangeTheme = Select a theme

#Access Logs:
lAccessLogs =  Access Logs
lClientAccessLogs = WiFi Client Access Logs
btnAddAccessLogsRule = Add Rule
ltDeviceName = Device Name
ltDestination = Destination
ltService = Service
ltFrequency = Frequency
ltMostRecestAccess = Most Recent Access
lAccessControl = Access Control
btnAccessControlRule = Add
ltACRuleName = Rule Name
ltACDeviceMac = Device MAC
ltACStartTime = Start Time
ltPdpName     = PDP Name
ltCid		= CID
ltIPType    = IP Type
ltIPv4Addr  = IPv4 Addr
ltIPv6Addr  = IPv6 Addr
ltACEndTime = End Time
ltIPAddr = IP Address
ltACMACAddr = Client MAC Address
ltACConTime = Connect Time
ltDisconTime =  Disconnect Time
h1InternetAccessRule = Internet Access Rule
h1InternetAccessRule1 = Add Internet Access Rule
lFromDevice = From Device:
lDeviceMac = Device MAC:
lAllowAccessOn = Allow Access On:
Sun = Sun
Mon = Mon
Tue = Tue
Wed = Wed
Thu = Thu
Fri = Fri
Sat = Sat
lDate = Date:

ltConnection = Connection

lLogStartTime = Start Time:
lLogEndTime = End Time:
EMPTY_RULE_NAME = Empty Rule name is not allowed
SPECIAL_CHARS_ARE_NOT_ALLOWED = Special characters like # , : , ' '(space), & , ; , ~ , | , < , > , $ are not allowed
MAC_IS_NOT_VALID = MAC address is invalid
IP_IS_NOT_VALID = IP address is invalid
INVALID_START_TIME = Invalid start time
INVALID_END_TIME = Invalid end time
START_TIME_LESS_ERROR = Start time should less than end time
lLogRulename = Rule Name:
# Begin Add by ycc, modify locale
# lproductName = ASR Wireless Router
lproductName = Wireless Router
# End Add by ycc, modify locale

#Quick Setup
h1UserSettings = User Settings
h1InternetConnectionQS = Internet Connection
h1InternetConnection = Internet Connection
h1WirelessSeetings = Wireless Settings
h1DevicePlaceGuid = Device Placement Guidelines
quickSetupText1 = Quick Setup
lUserSettings1 = User Settings
h1UserSettingsHeader = (It is recommended that you change the default  user name and password)
lInterConnQS = Choose the method to be used by the device to connect to the internet
lPrimaryQS = Wireless Network Settings
lDevicePlaceGuidText = It is recommended that you do not place your wireless router near a Microwave, Home Cordless Phone, a Baby Monitor,  Bluetooth devices (such as a headset or a Bluetooth keyboard or a Bluetooth mouse) and any other wireless network devices.
btnFinish = Finish
# Begin Add by ycc, modify locale
# QsText = Welcome! Thanks for choosing ASR
QsText = Welcome! Thanks for choosing our product
# End Add by ycc, modify locale
QsText1 = <B> We recommend running Quick Setup in order to set up the router.</B>
QsText2 = Please ensure that you have followed the steps in the Quick Setup Guide to make sure that all the cables are connected correctly. This configuration setup procedure will guide you using step-by-step instructions on how to get your Internet connection up and running.
QsText3 = Do not show Quick Setup in future.
QsText4 = Note - You will still be able to access the Quick Setup link on your dashboard even if you skip it here.
btnSkip = Skip
btnQuickSetup = Quick Setup

# Config Management
lConfOption = Configuration Options
lConfOptionText = Save Current Configuration to a File
lConfFile = Select a Configuration File
lConfFileText = Select the saved configuration file to restore settings.
lResotreFactSetting = Restore Factory Settings
lResotreFactSettingText =  This option resets the router to the factory defaults. The existing settings will be lost.
btnRestoreFactorySettings = Restore Factory Settings
btRestoreConf = Restore Configuration
btnSave =  Save
h1Rebooting = Reboot
lRebootingText = The gateway will now reboot.
lRebootingText1 = The new configuration will take effect after the reboot.
btnModalOk = OK
lConnect = Connect to wireless network
btnConnectW = Connect Anyway
btnConnectWP = Connect
h1Confirm = Confirm
lConfirmText = The existing settings will be lost. Continue?
btnModalCancle = Cancel
SelectConfFileError = Please select a configuration file


lConfirmText1 = Are you sure you want to restore to factory setting?
lConfirmTextReboot = Are you sure you want to reboot the router?
lConfirmText7 = restore default WPS security settings?
lConfirmText2 = The UI Theme has been changed.
lConfirmText3 = You will now be redirected to the login page.
lConfirmText4 = Clear your browser cache to see the new theme take effect.

lConfirmText5 = Turbo Mode settings have changed.
lConfirmText6 = The Router is rebooting for the settings to take effect.

lImportCfgFileText1 = Please choose configuration file(*.bin) to update.
lImportCfgFileText2 = Please make sure you connect to MIFI using USB cable before upgrade.
lImportCfgFileText3 = Device will reboot after update configuration file.
btnBrowserFile      = Browse
lFileFormatError    = File format error,please choose .bin file.
btnUpdate           = Update
lEmportCfgFileText	= Click below link to export configuration file.
lExportLink			= Export configuration file.
lExportCfgSuccess = Export configuration file successfully

#Reboot Router
lRebootRouter = Please press the button shown below to Reboot the Router
btRebootRouter = Reboot Router
h1RebootRouter = Router Reboot
lRebootedRouter = Reboot in progress please wait...
btnCancle=Cancel
btnRebootOK=OK
lQueryRebootedRouter=Are you sure to reboot the router?
lSaveAcatDumplogIntoSDText = This option will  require user to set save acat dump log to SD card or not, This log is for debug usage.
lSaveAcatDumplogSetting = Save Acatlog to SD Settings
lbtnSaveAcatDumpLogSetting = Save
lSdFormatSupportStatus = SD Format:
lSdSupportFormat0 = Support Format
lSdSupportFormat1 = Not Support Format
lSdSupportFormat2 = Not support,other reason
lSdSupportFormat255 = No SD Card

AllowModeError = Currently MAC Filter Mode is set to Allow. You may add only to Allow List.
DenyModeError = Currently MAC Filter Mode is set to Deny. You may add only to Deny List.
NetworkDisabledError = MAC Filters Settings for this network are not applicable, since this network is disabled.

#wireless Mac filter
ltAllowDeny = Wireless Client MAC Address
lDenyList = Deny List
thDeny = Wireless Client MAC Address
thAllow = Wireless Client MAC Address
lAllowList = Allow List
lMF = MAC Filters:
lMS = Mode Settings:
h1AddMACFilter = Add MAC Filter
lMACAddress =  MAC Address:
btnAddMACFilter = Add
lMacFilterItemError = Maximum 16 MAC address are allowed
lDeleteListMessage = Do you want to delete the MAC lists selected?
h1dellistConfigure = Delete Confirm
lMacEnableListEmpty = Don't allow any client access WIFI, disable MAC address filter function automatically
lAllowMacFilterPrompt = The MAC address of current client is not in the list, the operation will cause the current device unable to connect WIFI. Are you sue to continue?

#Turbo Mode
mTurboMode = Turbo Mode
lTurboModeSettings = Turbo mode is:
lTurboModeInfo = RSS Feeds, WiFi Protected Setup and Other (non-Primary) wireless networks will not be available if Turbo Mode is enabled.
h1ConfirmTurbo = ASR Wireless Router
h1ConfirmTurboWPS = ASR Wireless Router
h1ConfirmTurboUPnP = ASR Wireless Router
h1ConfirmTurboONW = ASR Wireless Router
h1ConfirmAccessSettings = ASR Wireless Router
lTurboWPSText1 = WiFi Protected Setup (WPS) can not be enabled in Turbo Mode.
lTurboUPnPText1 = Universal Plug and Play (UPnP) can not be enabled in Turbo Mode.
lTurboONWText1 = Other Wireless Networks can not be enabled in Turbo Mode.
lAccessSettingsText1 = The 'Restricted' Access is not supported yet.

errorSelectConfFile = Please select a configuration file
Microwave = Microwave
Bluetooth_Devices =Bluetooth Devices
Cordless_Phone = Cordless Phone
ownDevices = Other Wireless Network Devices
Baby_Monitor = Baby Monitor
lSleepTimeSetting= WIFI Auto Off Interval Time Setting

#SMS
tSms = SMS
mDeviceInbox = Device Inbox
mSimInbox	= SIM Inbox
mDeviceOutbox	= Device Outbox
mSimSms	= SIM SMS
mDrafts		= Drafts
mSmsSet		= SMS Settings

lsmsReceiver = Receiver

lt_SmsSet_stcTitle	= SMS Settings
lt_SmsSet_stcSaveLoc = SMS Save Location
lt_SmsSet_stcSaveLocSimCard	= SIM Card
lt_SmsSet_stcSaveLocDevice		= Device
lt_SmsSet_stcSendReportEnabled	= Enabled
lt_SmsSet_stcSendReportDisabled	= Disabled
lt_SmsSet_stcDeliveryReport	= Delivery Report
lt_SmsSet_stcValidity				= Validity
lt_SmsSet_stcTwelveHours			= 12 Hours
lt_SmsSet_stcOneDay					= One Day
lt_SmsSet_stcOneWeek				= One Week
lt_SmsSet_stcLargest				= Maxinum
lt_SmsSet_stcCenterNumber			= Center Number
lt_SmsSet_btnSave					= Save
lt_SmsSet_chkSavetoSim =  Save SMS to SIM in default
lt_SmsSet_stcSMSOverCSMode =   SMS Over Mode
lt_SmsSet_stcPackageDomain  =  SMS Over PS
lt_SmsSet_stcCircuitSwitched = SMS Over CS
lt_SmsSet_stcPackagePrefer  =  SMS Over PS Prefer
lt_SmsSet_stcCircuitSwitchedPrefer = SMS Over CS Prefer

lt_sms_stcTitle	= Inbox
lt_sms_stcFrom = From
lt_sms_stcSubject = Subject
lt_sms_stcRecvTime = Time
lt_sms_stcStatus = Status
lt_sms_stcLocation = Location
lt_sms_btnDelete	=Delete
lt_sms_btnNew		= New
lt_sms_btnRefresh   = Refresh
lt_sms_btnCopy      = Copy
lt_sms_btnMove      = Move
lt_sms_btnSaveDraft = Save
lt_sms_btnSend      = Send
lt_sms_btnCancel	= Cancel
lt_sms_chooseNumberTip        = You can choose 5 contacts at most.
lt_sms_forwardSmsTip        = Forward SMS
lt_sms_deleteSmsTip        = Delete SMS
lt_sms_newSmsTip        =  New SMS
lt_sms_unreadSms		= Unread SMS
lt_sms_readedSms		= Readed SMS
lt_sms_sendSuccess		= Send Success
lt_sms_sendFailed		= Send Failed
lt_sms_drafts			= Drafts
lt_sms_stcmeudelete        = Delete
lt_sms_stcmeucopy          = Copy
lt_sms_stcmeucopytosim     = Copy to SIM
lt_sms_stcmeucopytolocal   = Copy to Device
lt_sms_stcmeumovetosim     = Move to SIM
lt_sms_stcmeumovetolocal   = Move to Device
lt_sms_stcmeumove          = Move
lt_sms_stcmeusavenumber    = SaveNumber
lt_sms_stcmeuclearall      = ClearAll
lt_sms_stcmeurestore       = Restore
lsmsSimCardAbsent          = SIM is absent,please insert SIM card to try again!
lsmsNoWirelessNetwork      = No wireless network, please connect wireless network and try again!
lsmsWarning					= Warning
lDeviceInboxCapacityFull			= Device Inbox capacity is full, please delete.
lDeviceOutboxCapacityFull			= Device Outbox capacity is full, please delete.
lDeviceDraftboxCapacityFull			= Device Draftbox capacity is full, please delete.
lSimCardCapacityFull		= SIM card capacity is full, please delete.
lMessageReportTitle = Send Message Report
lMessageReportSuccessReceive = received sms successfully.
lMessageReportFailedReceive = received sms failed.
lSMSCenterModificationWarning = You modified smscenter number ,maybe it will casue send SMS failed!!!
lsmsNotification					= Notification
lsmsOneNewArrivedSMS					= new message arrived
lsmsMoreNewArrivedSMS					= new messages arrived
lSendMessageFailed = Send SMS Failed
lSaveMessageFailed = Save message failed
lDeleteMessageFailed = Delete message failed
lMoveMessageFailed = Move message failed
lCopyMessageFailed = Copy message failed
lCopyMessagePartialFailed = Copy message partial failed
lMoveMessagePartialFailed = Move message partial failed
lOperateMessageReportTitle = Message Operation
lt_sms_stcSmsLenghtError = Most send 640 english or 280 chinese characters
lContactIsEmpty = Phone number can't be empty
lSmsIsEmpty = SMS content is empty.
lSaveSmsError	= Only save one SMS to draftbox.
lPhoneNumberFormatError = Phone number format error

#Port Filter page
mPortFilter								= Port Filter
lt_portFilter_stcTitle					= Port Filter
lt_portFilter_stcMode					= Port Filter Mode
lt_portFilter_stcEnabledPortFilter		= Enabled
lt_portFilter_stcDisabledPortFilter		= Disabled
lt_portFilter_stcSave					= Save
lt_portFilter_btnAddPortFilterRules		= Add Port Filter
lt_portFilter_stcRuleName				= Rule Name
lt_portFilter_stcProtocol				= Protocol
lt_portFilter_stcTriggerPort			= Trigger Port
lt_portFilter_stcResponsePort			= Response Port
lt_portFilter_stcPortFilterDlgTitle		= Port Filter Dialog 
lt_portFilter_btnCancelAddRule			= Cancel
lt_portFilter_btnAddRule				= Add
lStartPortIsEmpty						= Trigger Port and Response Port can't be all empty
lStartPortLarger						= Start port number cannot be less than end port number
lPortNumInvalide						= The port number must be an integer between 1 and 65535
lTriggerPortIncomplete					= Trigger port number is imcomplete,please check
lResponsePortIncomplete					= Response port number is imcomplete,please check
#device traffic data
mDataTraffic							= Device Traffic
ltitle									= Device Traffic
lClientName								= Name
lDeviceName								= Name
lNameType								= Name Type
lNameTypeAssigned						= Assigned		
lNameTypeNotAssigned					= NotAssigned
deviceStatusSel							= Client Status
DisconnectedStatus						= Disconnected
ConnectedStatus							= Connected
BlockStatus								= Block
lConnType								= Connection Type
lIpAddr									= IP Address
lMacAddr								= MAC Address
lMacAddress								= MAC Address
lConnTime								= Total Connected Time
lTotalTraffic							= Total Data Traffic
lStatus									= Device Status
lDeviceInfoBoxTitle						= Device Information
lLastConTime							= Last Access Time
lTotalConTime							= Total Connected Time
lMonthSendData							= Month Send Data
lMonthRecvData							= Month Received Data
lMonthTotalData							= Month Total Data
lLast3DaySendData						= Last 3 Day Send Data
lLast3DayRecvData						= Last 3 Day Received Data
lLast3DayTotalData						= Last 3 Day Total Data
lTotalSendData							= Total Send Data
lTotalRecvData							= Total Received Data
lTotalData								= Total Used Data
lDeviceStatus							= Device Status
lBtnOk								= OK
lAction									= Action
lMaxBlockStatusTip						= Most blocked 8 clients��please unblock another client if you want block the client.
lBlock									= Block
lUnBlock								= Unblock
lStop									= Stop
lConnection								= Connected
lDisConnection							= Disconnected
lBlocked								= Blocked
lUnkownStatus							= Unknown
lBtnResetDevices						= Reset Data Traffic


#mTrafficSetting
mTrafficSetting					= Traffic Setting
lt_trafficSet_stcTitle			= Traffic Control Settings
lt_trafficSet_stcStaticMode		= Traffic Control Mode
lt_trafficSet_stcDisabled		= Disabled
lt_trafficSet_stcMonth			= Per Month
lt_trafficSet_stcPeriod			= Per Period
lt_trafficSet_stcMonthUsedTaffic			= Month Used Traffic
lt_trafficSet_btnCalMonthTraffic			= Calibration
lt_trafficSet_stcMonthTotalTaffic			= Month Limit Traffic 
lt_trafficSet_btnChangeMonthTotalTraffic	= Change
lt_trafficSet_stcPeriodUsedTaffic			= Period Used Traffic
lt_trafficSet_btnCalPeroidTraffic			= Calibration
lt_trafficSet_stcPeriodTotalTaffic			= Period Limit Traffic 
lt_trafficSet_btnChangePeroidTotalTraffic	= Change
lt_trafficSet_stcPeriodTime					= Starting and Ending Time Cycle 
lt_trafficSet_btnChangePeroidTime			= Change
lt_trafficSet_stcPeriodTimeSetting			= Period Time Setting
lt_trafficSet_stcPeriodTime					= Period Time Range
lt_trafficSet_stcPeriodStartTime			= Period Start Time
lt_trafficSet_stcPeriodEndTime				= Period End Time
lt_trafficSet_btnNewPeroidTime				= Save
lt_trafficSet_btnNewTraffic					= Save
lt_trafficSet_stcCancel						= Cancel
lt_trafficSet_stcNewTraffic					= Calibration Traffic
lt_trafficSet_stcMonthAvailableTraffic		= Month Avalible Traffic
lt_trafficSet_btnChangeMonthAvalibleTraffic = Change
lt_trafficSet_stcPeriodAvalibleTraffic		= Period Avalible Traffic
lt_trafficSet_btnChangePeroidAvalibleTraffic= Change
lt_trafficSet_btnSave						= Save
lt_trafficSet_stcTrafficSetting				= Traffic Calibration
TrafficCalLable							= Calibration Traffic
TrafficCalTile							= Traffic Calibration
MonthAvalibleTrafficSetting				= Month Avalible Traffic Setting
MonthAvalibleTraffic					= Month Avalible Traffic
PeriodAvalibleTrafficSetting				= Period Avalible Traffic Setting
PeriodAvalibleTraffic					= Period Avalible Traffic
MonthLimitTrafficSetting				= Month Limit Traffic Setting
MonthLimitTraffic						= Month Limit Traffic
PeriodLimitTrafficSetting				= Period Limit Traffic Setting
PeriodLimitTraffic						= Period Limit Traffic 
lTimeFormatError						= Period Time Format Error(2014/6/13) or time range error
lt_trafficSet_stcUnlimit				= Unlimit Period
lt_trafficSet_stcUnlimitPeriodUsedTaffic = Unlimit Period Used Traffic
lt_trafficSet_stcUnlimitPeriodAvailableTraffic = Unlimit Period Avalible Traffic
lt_trafficSet_stcUnlimitPeriodTotalTaffic  = Unlimit Period Limit Traffic
lt_trafficSet_btnCalUnlimitPeriodTraffic = Calibration
lt_trafficSet_btnChangeUnlimitPeriodAvalibleTraffic = Change
lt_trafficSet_btnChangeUnlimitPeriodTotalTraffic = Change
lt_trafficSet_stcDisconnectNetworkLabel = Disconnect network when traffic arrived at the limit
lt_trafficSet_stcNoAction = No Action
lt_trafficSet_stcDisconnect = Disconnect
PeriodTimeRangeSetError = Period range should include the current date

#power off router
mPowerOffRouter = Power Off Router
lPowerOffRouter = Please press the bebow button to Power-off the router.
h1PowerOffRouter = Power Off Router
btPowerOffRouter = Power Off Router
labelPowerOffRouter = Router is powering off...
lQueryPowerOffRouter = Are you sure to power off the router?
btnPowerOffOK = OK
btnModalCancle = Cancel

mUssd = USSD
lt_ussd_stcTitle = USSD Service
lt_ussd_stcUssdServiceNumber = Please Input USSD Business Number to Dial-Up
lt_ussd_btnDial = Dial
lt_ussd_stcRecvTitle = Short Message
lt_ussd_stcResponseTitle = Response Input
lt_ussd_btnCancel = Cancel
lt_ussd_btnSend = Send
UssdServiceNumberEmpty = USSD service number cannot be empty